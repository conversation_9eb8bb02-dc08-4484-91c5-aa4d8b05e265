# Ultimate EMFILE fix for Windows - combines all approaches

Write-Host "🚀 Ultimate EMFILE Fix for Windows" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Step 1: Kill all Node processes
Write-Host "🛑 Step 1: Killing all Node.js processes..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "npm" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 3

# Step 2: Clear all caches
Write-Host "🧹 Step 2: Clearing all caches..." -ForegroundColor Yellow
npm cache clean --force
if (Test-Path "node_modules/.vite") { Remove-Item -Recurse -Force "node_modules/.vite" }
if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
if (Test-Path ".vite") { Remove-Item -Recurse -Force ".vite" }

# Step 3: Increase system limits
Write-Host "⚙️ Step 3: Setting system optimizations..." -ForegroundColor Yellow
$env:NODE_OPTIONS = "--max-old-space-size=8192 --max-semi-space-size=512"
$env:UV_THREADPOOL_SIZE = "128"
$env:VITE_FORCE_OPTIMIZE_DEPS = "true"

# Step 4: Create minimal MUI icons replacement
Write-Host "📦 Step 4: Creating minimal MUI icons..." -ForegroundColor Yellow
$muiIconsPath = "node_modules/@mui/icons-material"
$muiIconsBackupPath = "node_modules/@mui/icons-material.backup"

if (Test-Path $muiIconsPath) {
    if (Test-Path $muiIconsBackupPath) {
        Remove-Item -Recurse -Force $muiIconsBackupPath
    }
    Move-Item $muiIconsPath $muiIconsBackupPath
    
    # Create minimal replacement
    New-Item -ItemType Directory -Path $muiIconsPath -Force | Out-Null
    New-Item -ItemType Directory -Path "$muiIconsPath/esm" -Force | Out-Null
    
    # Create index.js that exports everything from our proxy
    $indexContent = @"
export * from '../../../src/utils/muiIconsProxy.ts';
export { default } from '../../../src/utils/muiIconsProxy.ts';
"@
    $indexPath = Join-Path $muiIconsPath "index.js"
    Set-Content -Path $indexPath -Value $indexContent

    # Create package.json
    $packageJson = @"
{
  "name": "@mui/icons-material",
  "version": "5.0.0",
  "main": "index.js",
  "module": "index.js",
  "type": "module"
}
"@
    $packagePath = Join-Path $muiIconsPath "package.json"
    Set-Content -Path $packagePath -Value $packageJson
}

# Step 5: Force dependency re-optimization
Write-Host "🔄 Step 5: Forcing dependency re-optimization..." -ForegroundColor Yellow
if (Test-Path "node_modules/.vite") { Remove-Item -Recurse -Force "node_modules/.vite" }

Write-Host "✅ All fixes applied!" -ForegroundColor Green
Write-Host "" -ForegroundColor White
Write-Host "🚀 Starting development server..." -ForegroundColor Green

# Start with maximum optimizations
npm run dev

Write-Host "" -ForegroundColor White
Write-Host "🎉 Development server started successfully!" -ForegroundColor Green
Write-Host "📝 To restore original MUI icons later: npm run restore:icons" -ForegroundColor Cyan
