@echo off
echo Creating minimal MUI icons replacement...

REM Kill all Node processes
taskkill /F /IM node.exe 2>nul

REM Clear caches
npm cache clean --force

REM Backup and replace MUI icons
if exist "node_modules\@mui\icons-material.backup" rmdir /s /q "node_modules\@mui\icons-material.backup"
if exist "node_modules\@mui\icons-material" move "node_modules\@mui\icons-material" "node_modules\@mui\icons-material.backup"

REM Create minimal replacement
mkdir "node_modules\@mui\icons-material"

REM Create index.js
echo export * from '../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\index.js"
echo export { default } from '../../src/utils/muiIconsProxy.ts'; >> "node_modules\@mui\icons-material\index.js"

REM Create package.json
echo { > "node_modules\@mui\icons-material\package.json"
echo   "name": "@mui/icons-material", >> "node_modules\@mui\icons-material\package.json"
echo   "version": "5.0.0", >> "node_modules\@mui\icons-material\package.json"
echo   "main": "index.js", >> "node_modules\@mui\icons-material\package.json"
echo   "module": "index.js", >> "node_modules\@mui\icons-material\package.json"
echo   "type": "module" >> "node_modules\@mui\icons-material\package.json"
echo } >> "node_modules\@mui\icons-material\package.json"

REM Create individual icon files for the ones we use
echo Creating individual icon files...

REM Launch icon
echo import { LaunchIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Launch.js"
echo export default LaunchIcon; >> "node_modules\@mui\icons-material\Launch.js"

REM UploadFile icon
echo import { UploadFileIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\UploadFile.js"
echo export default UploadFileIcon; >> "node_modules\@mui\icons-material\UploadFile.js"

REM BarChart icon
echo import { BarChartIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\BarChart.js"
echo export default BarChartIcon; >> "node_modules\@mui\icons-material\BarChart.js"

REM Preview icon
echo import { PreviewIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Preview.js"
echo export default PreviewIcon; >> "node_modules\@mui\icons-material\Preview.js"

REM Download icon
echo import { DownloadIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Download.js"
echo export default DownloadIcon; >> "node_modules\@mui\icons-material\Download.js"

REM Edit icon
echo import { EditIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Edit.js"
echo export default EditIcon; >> "node_modules\@mui\icons-material\Edit.js"

REM Insights icon
echo import { InsightsIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Insights.js"
echo export default InsightsIcon; >> "node_modules\@mui\icons-material\Insights.js"

REM Slideshow icon
echo import { SlideshowIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Slideshow.js"
echo export default SlideshowIcon; >> "node_modules\@mui\icons-material\Slideshow.js"

REM Article icon
echo import { ArticleIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Article.js"
echo export default ArticleIcon; >> "node_modules\@mui\icons-material\Article.js"

REM Share icon
echo import { ShareIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Share.js"
echo export default ShareIcon; >> "node_modules\@mui\icons-material\Share.js"

REM Description icon
echo import { DescriptionIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Description.js"
echo export default DescriptionIcon; >> "node_modules\@mui\icons-material\Description.js"

REM Business icon
echo import { BusinessIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Business.js"
echo export default BusinessIcon; >> "node_modules\@mui\icons-material\Business.js"

REM Circle icon
echo import { CircleIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Circle.js"
echo export default CircleIcon; >> "node_modules\@mui\icons-material\Circle.js"

REM ExpandMore icon
echo import { ExpandMoreIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\ExpandMore.js"
echo export default ExpandMoreIcon; >> "node_modules\@mui\icons-material\ExpandMore.js"

REM AccessTime icon
echo import { AccessTimeIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\AccessTime.js"
echo export default AccessTimeIcon; >> "node_modules\@mui\icons-material\AccessTime.js"

REM Info icon
echo import { InfoIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Info.js"
echo export default InfoIcon; >> "node_modules\@mui\icons-material\Info.js"

REM TipsAndUpdates icon
echo import { TipsAndUpdatesIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\TipsAndUpdates.js"
echo export default TipsAndUpdatesIcon; >> "node_modules\@mui\icons-material\TipsAndUpdates.js"

REM NetworkCheck icon
echo import { NetworkCheckIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\NetworkCheck.js"
echo export default NetworkCheckIcon; >> "node_modules\@mui\icons-material\NetworkCheck.js"

REM CloudUpload icon
echo import { CloudUploadIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\CloudUpload.js"
echo export default CloudUploadIcon; >> "node_modules\@mui\icons-material\CloudUpload.js"

REM ArrowBack icon
echo import { ArrowBackIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\ArrowBack.js"
echo export default ArrowBackIcon; >> "node_modules\@mui\icons-material\ArrowBack.js"

REM ContentCopy icon
echo import { ContentCopyIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\ContentCopy.js"
echo export default ContentCopyIcon; >> "node_modules\@mui\icons-material\ContentCopy.js"

REM Close icon
echo import { CloseIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Close.js"
echo export default CloseIcon; >> "node_modules\@mui\icons-material\Close.js"

REM Restore icon
echo import { RestoreIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Restore.js"
echo export default RestoreIcon; >> "node_modules\@mui\icons-material\Restore.js"

REM Dataset icon
echo import { DatasetIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Dataset.js"
echo export default DatasetIcon; >> "node_modules\@mui\icons-material\Dataset.js"

REM ErrorOutline icon
echo import { ErrorOutlineIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\ErrorOutline.js"
echo export default ErrorOutlineIcon; >> "node_modules\@mui\icons-material\ErrorOutline.js"

REM Refresh icon
echo import { RefreshIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Refresh.js"
echo export default RefreshIcon; >> "node_modules\@mui\icons-material\Refresh.js"

REM Home icon
echo import { HomeIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Home.js"
echo export default HomeIcon; >> "node_modules\@mui\icons-material\Home.js"

REM Visibility icon
echo import { VisibilityIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Visibility.js"
echo export default VisibilityIcon; >> "node_modules\@mui\icons-material\Visibility.js"

REM MoreHoriz icon
echo import { MoreHorizIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\MoreHoriz.js"
echo export default MoreHorizIcon; >> "node_modules\@mui\icons-material\MoreHoriz.js"

REM WarningAmber icon
echo import { WarningAmberIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\WarningAmber.js"
echo export default WarningAmberIcon; >> "node_modules\@mui\icons-material\WarningAmber.js"

REM WifiOff icon
echo import { WifiOffIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\WifiOff.js"
echo export default WifiOffIcon; >> "node_modules\@mui\icons-material\WifiOff.js"

REM Image icon
echo import { ImageIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Image.js"
echo export default ImageIcon; >> "node_modules\@mui\icons-material\Image.js"

REM TextFields icon
echo import { TextFieldsIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\TextFields.js"
echo export default TextFieldsIcon; >> "node_modules\@mui\icons-material\TextFields.js"

REM Lightbulb icon
echo import { LightbulbIcon } from '../../../src/utils/muiIconsProxy.ts'; > "node_modules\@mui\icons-material\Lightbulb.js"
echo export default LightbulbIcon; >> "node_modules\@mui\icons-material\Lightbulb.js"

echo Minimal MUI icons created successfully!
echo Starting development server...
npm run dev
