# PowerShell script to create minimal MUI icons replacement
Write-Host "Creating minimal MUI icons replacement..." -ForegroundColor Cyan

$muiIconsPath = "node_modules/@mui/icons-material"
$muiIconsBackupPath = "node_modules/@mui/icons-material.backup"

# Backup original if it exists
if (Test-Path $muiIconsPath) {
    Write-Host "Backing up original MUI icons..." -ForegroundColor Yellow
    if (Test-Path $muiIconsBackupPath) {
        Remove-Item -Recurse -Force $muiIconsBackupPath
    }
    Move-Item $muiIconsPath $muiIconsBackupPath
}

# Create minimal replacement directory
Write-Host "Creating minimal MUI icons directory..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path $muiIconsPath -Force | Out-Null

# List of all icons used in the codebase
$iconsUsed = @(
    "Launch", "UploadFile", "BarChart", "Preview", "Download", "Edit", "Insights",
    "Slideshow", "Article", "Share", "Description", "Business", "Circle",
    "ExpandMore", "AccessTime", "Info", "TipsAndUpdates", "NetworkCheck",
    "CloudUpload", "ArrowBack", "ContentCopy", "Close", "Restore", "Dataset",
    "ErrorOutline", "Refresh", "Home", "Visibility", "MoreHoriz", "WarningAmber",
    "WifiOff", "Image", "TextFields", "Lightbulb"
)

# Create individual icon files that export from our proxy
foreach ($icon in $iconsUsed) {
    $iconContent = @"
// Minimal MUI icon replacement - exports from proxy
import { ${icon}Icon } from '../../../src/utils/muiIconsProxy.ts';
export default ${icon}Icon;
"@
    Set-Content -Path "$muiIconsPath/$icon.js" -Value $iconContent
    
    # Also create .d.ts file for TypeScript
    $iconTypeContent = @"
import { SvgIconProps } from '@mui/material/SvgIcon';
import { OverridableComponent, OverridableTypeMap } from '@mui/material/OverridableComponent';

declare const $icon: OverridableComponent<OverridableTypeMap>;
export default $icon;
"@
    Set-Content -Path "$muiIconsPath/$icon.d.ts" -Value $iconTypeContent
}

# Create main index.js that exports everything from our proxy
$indexContent = @"
// Minimal MUI icons replacement - exports from proxy
export * from '../../src/utils/muiIconsProxy.ts';
export { default } from '../../src/utils/muiIconsProxy.ts';
"@
Set-Content -Path "$muiIconsPath/index.js" -Value $indexContent

# Create index.d.ts for TypeScript
$indexTypeContent = @"
export * from '../../src/utils/muiIconsProxy';
export { default } from '../../src/utils/muiIconsProxy';
"@
Set-Content -Path "$muiIconsPath/index.d.ts" -Value $indexTypeContent

# Create package.json
$packageJson = @"
{
  "name": "@mui/icons-material",
  "version": "5.0.0",
  "main": "index.js",
  "module": "index.js",
  "types": "index.d.ts",
  "type": "module"
}
"@
Set-Content -Path "$muiIconsPath/package.json" -Value $packageJson

Write-Host "Minimal MUI icons created successfully!" -ForegroundColor Green
Write-Host "Icons directory now contains only the icons we use, preventing EMFILE errors." -ForegroundColor Cyan
