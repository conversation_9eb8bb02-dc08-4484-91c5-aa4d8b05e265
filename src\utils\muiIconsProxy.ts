// Proxy file to handle MUI icons without loading the entire library
// This prevents EMFILE errors by providing a single entry point

import React from 'react';

// Create specific icon components with appropriate symbols
const createIcon = (symbol: string, displayName?: string) => {
  const IconComponent: React.FC<any> = (props) => {
    return React.createElement('span', {
      ...props,
      'data-icon': displayName || symbol,
      style: {
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '24px',
        height: '24px',
        fontSize: '16px',
        lineHeight: '1',
        fontFamily: 'system-ui, -apple-system, sans-serif',
        userSelect: 'none',
        ...props.style
      }
    }, symbol);
  };
  IconComponent.displayName = displayName || 'ProxyIcon';
  return IconComponent;
};

// All icon exports with specific symbols
export const ArrowBackIcon = createIcon('←', 'ArrowBack');
export const ContentCopyIcon = createIcon('📋', 'ContentCopy');
export const DownloadIcon = createIcon('⬇', 'Download');
export const CloudUploadIcon = createIcon('☁', 'CloudUpload');
export const BarChartIcon = createIcon('📊', 'BarChart');
export const PreviewIcon = createIcon('👁', 'Preview');
export const EditIcon = createIcon('✏', 'Edit');
export const InsightsIcon = createIcon('💡', 'Insights');
export const CloseIcon = createIcon('✕', 'Close');
export const ExpandMoreIcon = createIcon('⌄', 'ExpandMore');
export const RestoreIcon = createIcon('↻', 'Restore');
export const DatasetIcon = createIcon('📄', 'Dataset');
export const LaunchIcon = createIcon('🚀', 'Launch');
export const UploadFileIcon = createIcon('📁', 'UploadFile');
export const ErrorOutlineIcon = createIcon('⚠', 'ErrorOutline');
export const RefreshIcon = createIcon('↻', 'Refresh');
export const HomeIcon = createIcon('🏠', 'Home');
export const VisibilityIcon = createIcon('👁', 'Visibility');
export const CircleIcon = createIcon('●', 'Circle');
export const MoreHorizIcon = createIcon('⋯', 'MoreHoriz');
export const WarningAmberIcon = createIcon('⚠', 'WarningAmber');
export const AccessTimeIcon = createIcon('⏰', 'AccessTime');
export const WifiOffIcon = createIcon('📶', 'WifiOff');
export const InfoIcon = createIcon('ℹ', 'Info');
export const TipsAndUpdatesIcon = createIcon('💡', 'TipsAndUpdates');
export const NetworkCheckIcon = createIcon('🌐', 'NetworkCheck');
export const ImageIcon = createIcon('🖼', 'Image');
export const TextFieldsIcon = createIcon('📝', 'TextFields');
export const StartIcon = createIcon('🚀', 'Launch'); // Alias for LaunchIcon
export const PresentationIcon = createIcon('📊', 'Slideshow');
export const ArticleIcon = createIcon('📄', 'Article');
export const ShareIcon = createIcon('📤', 'Share');
export const DescriptionIcon = createIcon('📋', 'Description');
export const BusinessIcon = createIcon('🏢', 'Business');
export const MenuIcon = createIcon('☰', 'Menu');
export const SlideshowIcon = createIcon('📊', 'Slideshow');
export const LightbulbIcon = createIcon('💡', 'Lightbulb');

// Export without Icon suffix for destructured imports (ErrorModal uses these)
export const Warning = createIcon('⚠', 'Warning');
export const Close = createIcon('✕', 'Close');
export const ExpandMore = createIcon('⌄', 'ExpandMore');
export const Refresh = createIcon('↻', 'Refresh');
export const ArrowBack = createIcon('←', 'ArrowBack');
export const Info = createIcon('ℹ', 'Info');
export const Menu = createIcon('☰', 'Menu');

// Default export - this will be used when importing '@mui/icons-material'
const GenericIcon = createIcon('📊', 'GenericIcon');
export default GenericIcon;
