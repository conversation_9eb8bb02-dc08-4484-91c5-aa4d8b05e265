{"name": "interactive-dashboard", "private": true, "version": "1.0.1", "type": "module", "scripts": {"dev": "vite --mode development --host localhost --port 5173", "dev:windows": "set NODE_OPTIONS=--max-old-space-size=4096 && vite --mode development --host localhost --port 5173", "dev:prod": "vite --mode production --host localhost --port 5173", "dev:original": "vite", "dev:simple": "npx http-server public -p 5173 -c-1", "dev:no-hmr": "vite build && vite preview", "build": "vite build", "build:check": "tsc -b && vite build", "build:prod": "cross-env VITE_APP_VERSION=%npm_package_version% vite build", "build:version": "npm run build:prod", "lint": "eslint .", "preview": "vite preview", "version:bump": "npm version patch && npm run build:version", "deploy": "node scripts/deploy.js", "deploy:bump": "node scripts/deploy.js --bump", "deploy:quick": "node scripts/deploy.js --skip-build", "restart:windows": "powershell -ExecutionPolicy Bypass -File scripts/restart-dev-windows.ps1", "restart:windows:cmd": "scripts\\restart-dev-windows.bat", "fix:emfile": "powershell -ExecutionPolicy Bypass -File scripts/fix-emfile-windows.ps1", "fix:emfile:ultimate": "powershell -ExecutionPolicy Bypass -File scripts/ultimate-emfile-fix.ps1", "fix:emfile:permanent": "powershell -ExecutionPolicy Bypass -File scripts/permanent-emfile-fix.ps1", "restore:icons": "powershell -ExecutionPolicy Bypass -File scripts/restore-icons-windows.ps1", "clean": "rimraf dist node_modules/.vite .vite && npm cache clean --force"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^6.1.6", "@mui/material": "^6.1.6", "@react-oauth/google": "^0.12.2", "axios": "^1.8.4", "framer-motion": "^11.11.17", "html-to-image": "^1.11.13", "plotly.js": "^2.35.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-plotly.js": "^2.6.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "cross-env": "^7.0.3", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}