import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// Plugin to handle MUI icon imports
const muiIconsPlugin = () => ({
  name: 'mui-icons-plugin',
  resolveId(id: string) {
    if (id.startsWith('@mui/icons-material/')) {
      const iconName = id.replace('@mui/icons-material/', '');
      return `virtual:mui-icon:${iconName}`;
    }
    if (id === '@mui/icons-material') {
      return 'virtual:mui-icons-all';
    }
  },
  load(id: string) {
    if (id.startsWith('virtual:mui-icon:')) {
      const iconName = id.replace('virtual:mui-icon:', '');
      // Map icon names to our proxy exports
      const iconMap: Record<string, string> = {
        'Download': 'DownloadIcon',
        'ContentCopy': 'ContentCopyIcon',
        'Visibility': 'VisibilityIcon',
        'Edit': 'EditIcon',
        'Lightbulb': 'LightbulbIcon',
        'Circle': 'CircleIcon',
        'Close': 'CloseIcon',
        'MoreHoriz': 'MoreHorizIcon',
        'ArrowBack': 'ArrowBackIcon',
        'Restore': 'RestoreIcon',
        'Dataset': 'DatasetIcon',
        'ErrorOutline': 'ErrorOutlineIcon',
        'Refresh': 'RefreshIcon',
        'Home': 'HomeIcon',
        'WarningAmber': 'WarningAmberIcon',
        'AccessTime': 'AccessTimeIcon',
        'WifiOff': 'WifiOffIcon',
        'Info': 'InfoIcon',
        'TipsAndUpdates': 'TipsAndUpdatesIcon',
        'NetworkCheck': 'NetworkCheckIcon',
        'CloudUpload': 'CloudUploadIcon',
        'Insights': 'InsightsIcon',
        'ExpandMore': 'ExpandMoreIcon',
        'Image': 'ImageIcon',
        'TextFields': 'TextFieldsIcon',
        'Launch': 'LaunchIcon',
        'UploadFile': 'UploadFileIcon',
        'BarChart': 'BarChartIcon',
        'Preview': 'PreviewIcon',
        'Slideshow': 'SlideshowIcon',
        'Article': 'ArticleIcon',
        'Share': 'ShareIcon',
        'Description': 'DescriptionIcon',
        'Business': 'BusinessIcon'
      };

      const proxyIconName = iconMap[iconName] || 'GenericIcon';
      return `
        import { ${proxyIconName} } from '/src/utils/muiIconsProxy.ts';
        export default ${proxyIconName};
      `;
    }
    if (id === 'virtual:mui-icons-all') {
      return `export * from '/src/utils/muiIconsProxy.ts';`;
    }
  }
});

// Configuration optimized for Windows file system limits
export default defineConfig({
  plugins: [
    react({
      fastRefresh: false, // Disable React Refresh
    }),
    muiIconsPlugin()
  ],
  server: {
    port: 5173,
    host: 'localhost',
    hmr: false, // Disable HMR completely
    watch: {
      // Reduce file watching to prevent EMFILE errors
      usePolling: false,
      interval: 1000,
      binaryInterval: 2000,
      ignored: [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**',
        '**/.vscode/**',
        '**/.idea/**',
        '**/coverage/**',
        '**/*.log',
        '**/.env*',
        '**/package-lock.json',
        '**/yarn.lock'
      ]
    }
  },
  resolve: {
    dedupe: ['react', 'react-dom', '@emotion/react', '@emotion/styled']
  },
  // Aggressively optimize dependencies and reduce file handles
  optimizeDeps: {
    exclude: [
      '@mui/icons-material'
    ],
    include: [
      'react',
      'react-dom',
      '@mui/material',
      '@emotion/react',
      '@emotion/styled',
      'plotly.js-dist-min'
    ],
    // Force dependency optimization to reduce file handles
    force: true,
    // Completely ignore MUI icons during dependency scanning
    entries: [
      'src/**/*.{ts,tsx}',
      '!src/**/*.d.ts'
    ]
  },
  // Build optimizations to reduce file system pressure
  build: {
    rollupOptions: {
      external: [
        // Treat MUI icons as external to prevent bundling
        /@mui\/icons-material\/.*/
      ],
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@emotion/react', '@emotion/styled'],
          plotly: ['plotly.js-dist-min']
        },
        // Handle external MUI icons
        globals: {
          '@mui/icons-material': 'MuiIcons'
        }
      }
    },
    // Reduce concurrent file operations
    chunkSizeWarningLimit: 1000
  },
  // Reduce file system operations
  esbuild: {
    logOverride: { 'this-is-undefined-in-esm': 'silent' }
  }
})
