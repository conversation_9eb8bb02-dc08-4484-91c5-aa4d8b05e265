import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// Configuration optimized for Windows file system limits and EMFILE prevention
export default defineConfig({
  plugins: [
    react({
      fastRefresh: false, // Disable React Refresh to prevent file handle leaks
    })
  ],
  server: {
    port: 5173,
    host: 'localhost',
    hmr: false, // Disable HMR completely to prevent file watching issues
    watch: {
      // Aggressive file watching reduction to prevent EMFILE errors
      usePolling: false,
      interval: 2000, // Increased interval to reduce file system pressure
      binaryInterval: 3000, // Increased binary interval
      ignored: [
        // Exclude all node_modules to prevent scanning thousands of files
        '**/node_modules/**',
        // Specifically exclude MUI icons to prevent EMFILE
        '**/node_modules/@mui/icons-material/**',
        '**/node_modules/@mui/icons-material',
        // Standard exclusions
        '**/.git/**',
        '**/dist/**',
        '**/build/**',
        '**/.vscode/**',
        '**/.idea/**',
        '**/coverage/**',
        '**/*.log',
        '**/.env*',
        '**/package-lock.json',
        '**/yarn.lock',
        // Additional exclusions to reduce file watching
        '**/node_modules/.cache/**',
        '**/node_modules/.vite/**',
        '**/.cache/**',
        '**/temp/**',
        '**/tmp/**'
      ]
    }
  },
  resolve: {
    dedupe: ['react', 'react-dom', '@emotion/react', '@emotion/styled'],
    // Alias MUI icons to our proxy to prevent Vite from scanning the icons directory
    alias: {
      '@mui/icons-material': '/src/utils/muiIconsProxy.ts'
    }
  },
  // Optimize dependencies to reduce file handles and prevent EMFILE
  optimizeDeps: {
    // Exclude MUI icons completely to prevent file scanning
    exclude: [
      '@mui/icons-material',
      '@mui/icons-material/*'
    ],
    // Include only essential dependencies
    include: [
      'react',
      'react-dom',
      '@mui/material',
      '@emotion/react',
      '@emotion/styled'
    ],
    // Force optimization to prevent dynamic imports from scanning files
    force: true
  },
  // Build optimizations to reduce file system pressure
  build: {
    // Reduce concurrent operations to prevent file handle exhaustion
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@emotion/react', '@emotion/styled']
        }
      }
    }
  },
  // Reduce file system operations during development
  esbuild: {
    logOverride: { 'this-is-undefined-in-esm': 'silent' }
  }
})
