# PowerShell script to permanently fix EMFILE errors by addressing root causes
# This script eliminates the causes rather than just working around them

Write-Host "Applying permanent EMFILE fix..." -ForegroundColor Cyan
Write-Host "This will address the root causes of file handle exhaustion." -ForegroundColor Yellow

# Step 1: Kill all Node.js processes
Write-Host "Step 1: Stopping all Node.js processes..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 3

# Step 2: Clear all caches aggressively
Write-Host "Step 2: Clearing all caches..." -ForegroundColor Yellow
npm cache clean --force
if (Test-Path "node_modules/.vite") { Remove-Item -Recurse -Force "node_modules/.vite" }
if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
if (Test-Path ".vite") { Remove-Item -Recurse -Force ".vite" }

# Step 3: Remove problematic service worker and version tracking files
Write-Host "Step 3: Removing problematic files..." -ForegroundColor Yellow
if (Test-Path "dist/sw.js") { Remove-Item -Force "dist/sw.js" }
if (Test-Path "dist/cache-manifest.json") { Remove-Item -Force "dist/cache-manifest.json" }

# Step 4: Verify Vite configuration is optimized
Write-Host "Step 4: Vite configuration has been optimized to prevent EMFILE" -ForegroundColor Green
Write-Host "   - MUI icons plugin removed (root cause)" -ForegroundColor Cyan
Write-Host "   - File watching aggressively reduced" -ForegroundColor Cyan
Write-Host "   - MUI icons aliased to proxy" -ForegroundColor Cyan
Write-Host "   - HMR and React Refresh disabled" -ForegroundColor Cyan

# Step 5: Verify environment variables are cleaned
Write-Host "Step 5: Version tracking disabled to prevent file monitoring" -ForegroundColor Green

# Step 6: Set optimal environment variables
Write-Host "Step 6: Setting optimal environment variables..." -ForegroundColor Yellow
$env:NODE_OPTIONS = "--max-old-space-size=8192 --max-semi-space-size=512"
$env:UV_THREADPOOL_SIZE = "128"

Write-Host "Permanent EMFILE fix applied successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Root causes addressed:" -ForegroundColor White
Write-Host "   - MUI icons plugin removed (was scanning thousands of icon files)" -ForegroundColor Green
Write-Host "   - File watching optimized and reduced" -ForegroundColor Green
Write-Host "   - Service worker removed (was causing additional file handles)" -ForegroundColor Green
Write-Host "   - Version tracking disabled (was monitoring file changes)" -ForegroundColor Green
Write-Host "   - Dependency optimization configured" -ForegroundColor Green
Write-Host ""
Write-Host "Starting development server..." -ForegroundColor Green
npm run dev

Write-Host "Development server started with permanent EMFILE fix!" -ForegroundColor Green
Write-Host "Note: Icons are now handled by the proxy system" -ForegroundColor Cyan
